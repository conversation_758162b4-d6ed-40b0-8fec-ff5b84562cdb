import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  Container,
  Grid,
  Stepper,
  Step,
  StepLabel,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  CircularProgress,
  Alert,
  Autocomplete,
  Chip,
  OutlinedInput,
  InputAdornment,
  FormControlLabel,
  Switch,
  Slider,
} from '@mui/material';
import {
  School,
  AttachMoney,
  Person,
  Star,
  CheckCircle,
} from '@mui/icons-material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '../../contexts/AuthContext';
import { getCourses, Course } from '../../api/courses';
import { createMyTutorProfile, getMyTutorProfile, TutorProfileCreate } from '../../api/tutors';

// Define steps for the tutor profile completion process
const steps = ['Personal Info', 'Teaching Experience', 'Specializations', 'Rates & Availability', 'Confirmation'];

// Helper function to generate hash code for custom subjects
declare global {
  interface String {
    hashCode(): number;
  }
}

String.prototype.hashCode = function() {
  let hash = 0;
  if (this.length === 0) return hash;
  for (let i = 0; i < this.length; i++) {
    const char = this.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return Math.abs(hash);
};

// Validation schemas for each step
const validationSchemas = [
  // Step 1: Personal Info
  Yup.object({
    bio: Yup.string()
      .min(50, 'Bio must be at least 50 characters')
      .max(1000, 'Bio must be at most 1000 characters')
      .required('Bio is required'),
  }),
  // Step 2: Teaching Experience & Personal Details
  Yup.object({
    experience_years: Yup.number()
      .min(0, 'Experience years must be at least 0')
      .max(50, 'Experience years must be at most 50')
      .required('Experience years is required'),
    location: Yup.string()
      .max(100, 'Location must be less than 100 characters')
      .required('Location is required'),
    gender: Yup.string()
      .oneOf(['male', 'female', 'other', 'prefer_not_to_say'], 'Please select a valid gender')
      .required('Gender is required'),
    languages: Yup.array()
      .of(Yup.string())
      .min(1, 'Please select at least one language')
      .max(10, 'Please select at most 10 languages')
      .required('Languages spoken is required'),
    linkedin_url: Yup.string().url('Please enter a valid LinkedIn URL'),
    website_url: Yup.string().url('Please enter a valid website URL'),
  }),
  // Step 3: Specializations
  Yup.object({
    specialization_ids: Yup.array()
      .min(1, 'Please select at least one specialization')
      .max(3, 'Please select at most 3 specializations')
      .required('Specializations are required'),
  }),
  // Step 4: Rates & Availability
  Yup.object({
    hourly_rate: Yup.number()
      .min(500, 'Hourly rate must be at least ₦500')
      .max(50000, 'Hourly rate must be at most ₦50,000')
      .required('Hourly rate is required'),
    preferred_session_type: Yup.string()
      .oneOf(['online', 'in_person', 'both'])
      .required('Session type preference is required'),
    max_students_per_session: Yup.number()
      .min(1, 'Must allow at least 1 student')
      .max(20, 'Cannot exceed 20 students per session')
      .required('Max students per session is required'),
  }),
  // Step 5: Confirmation (no validation needed)
  Yup.object({}),
];

const TutorProfileCompletion: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [activeStep, setActiveStep] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [profileComplete, setProfileComplete] = useState(false);

  // Fallback courses in case API fails
  const fallbackCourses = [
    { id: 1, name: 'Mathematics', code: 'MTH101', description: 'General Mathematics' },
    { id: 2, name: 'Physics', code: 'PHY101', description: 'General Physics' },
    { id: 3, name: 'Chemistry', code: 'CHM101', description: 'General Chemistry' },
    { id: 4, name: 'Computer Science', code: 'CSC101', description: 'Introduction to Computing' },
    { id: 5, name: 'Economics', code: 'ECO101', description: 'Principles of Economics' },
    { id: 6, name: 'English', code: 'ENG101', description: 'English Language' },
    { id: 7, name: 'Biology', code: 'BIO101', description: 'General Biology' },
  ];

  // Fetch courses for specializations
  const {
    data: apiCourses = [],
    isLoading: isLoadingCourses,
    error: coursesError
  } = useQuery({
    queryKey: ['courses'],
    queryFn: getCourses,
    retry: 3,
    staleTime: 5 * 60 * 1000, // 5 minutes
    onSuccess: (data) => {
      console.log('Courses loaded successfully:', data);
    },
    onError: (error) => {
      console.error('Failed to load courses:', error);
    },
  });

  // Use API courses if available, otherwise use fallback
  const courses = apiCourses.length > 0 ? apiCourses : fallbackCourses;

  // Check if tutor profile already exists on component mount
  useEffect(() => {
    const checkExistingProfile = async () => {
      try {
        const profile = await getMyTutorProfile();
        // If we get here, profile exists
        console.log('Tutor profile already exists:', profile);
        console.log('Redirecting to tutor dashboard');
        navigate('/tutor/dashboard', { replace: true });
      } catch (error: any) {
        // 404 means no profile exists, which is expected for this page
        if (error.response?.status !== 404) {
          console.error('Error checking existing tutor profile:', error);
        } else {
          console.log('No existing tutor profile found, staying on completion page');
        }
      }
    };

    checkExistingProfile();
  }, [navigate]);

  // Create tutor profile mutation
  const createProfileMutation = useMutation({
    mutationFn: (data: TutorProfileCreate) => createMyTutorProfile(data),
    onSuccess: async () => {
      try {
        // Invalidate queries first
        queryClient.invalidateQueries({ queryKey: ['tutorProfile'] });
        queryClient.invalidateQueries({ queryKey: ['user'] });

        // Refresh user data to update profile completion status
        await refreshUser();

        setProfileComplete(true);

        // Navigate immediately after successful refresh
        setTimeout(() => {
          navigate('/tutor/dashboard', { replace: true });
        }, 1500);
      } catch (error) {
        console.error('Error refreshing user after profile creation:', error);
        // Navigate anyway to prevent being stuck
        setTimeout(() => {
          navigate('/tutor/dashboard', { replace: true });
        }, 2000);
      }
    },
    onError: (err: any) => {
      // Check if the error is because profile already exists
      if (err.response?.status === 400 && err.response?.data?.detail?.includes('already exists')) {
        // Profile already exists, redirect to tutor dashboard
        console.log('Tutor profile already exists, redirecting to tutor dashboard');
        navigate('/tutor/dashboard', { replace: true });
        return;
      }
      setError(err.response?.data?.detail || 'Failed to create tutor profile. Please try again.');
    },
  });

  const formik = useFormik({
    initialValues: {
      bio: '',
      experience_years: 0,
      hourly_rate: 2500,
      is_available: true,
      preferred_session_type: 'both' as 'online' | 'in_person' | 'both',
      max_students_per_session: 1,
      linkedin_url: '',
      website_url: '',
      location: '',
      gender: '' as 'male' | 'female' | 'other' | 'prefer_not_to_say' | '',
      languages: [] as string[],
      specialization_ids: [] as number[],
      custom_specializations: [] as string[], // Track custom specializations separately
    },
    validationSchema: validationSchemas[activeStep],
    onSubmit: async (values) => {
      if (activeStep < steps.length - 1) {
        handleNext();
        return;
      }

      // On the last step, submit the form
      setError(null);

      // Prepare the data with custom specializations
      const submitData = {
        ...values,
        custom_specializations: values.custom_specializations || []
      };

      createProfileMutation.mutate(submitData);
    },
  });

  const handleNext = () => {
    const currentSchema = validationSchemas[activeStep];
    try {
      currentSchema.validateSync(formik.values, { abortEarly: false });
      setActiveStep((prevStep) => prevStep + 1);
      setError(null);
    } catch (err: any) {
      // Get the fields that are relevant for the current step
      const stepFields = getStepFields(activeStep);

      // Trigger validation to show errors only for current step fields
      stepFields.forEach(key => {
        formik.setFieldTouched(key, true);
      });
      setError('Please fix the errors before proceeding.');
    }
  };

  // Helper function to get fields for each step
  const getStepFields = (step: number): string[] => {
    switch (step) {
      case 0: return ['bio'];
      case 1: return ['experience_years', 'location', 'gender', 'languages', 'linkedin_url', 'website_url'];
      case 2: return ['specialization_ids'];
      case 3: return ['hourly_rate', 'preferred_session_type', 'max_students_per_session'];
      default: return [];
    }
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const getStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Box sx={{ px: { xs: 1, sm: 0 } }}>
            <Typography variant="h6" gutterBottom sx={{ fontSize: { xs: '1.1rem', sm: '1.25rem' } }}>
              Tell us about yourself
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph sx={{ mb: 3 }}>
              Write a compelling bio that highlights your teaching experience and passion for education.
            </Typography>

            <TextField
              fullWidth
              multiline
              rows={{ xs: 4, sm: 6 }}
              name="bio"
              label="Bio"
              placeholder="I am a passionate educator with expertise in... I enjoy helping students..."
              value={formik.values.bio}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={formik.touched.bio && Boolean(formik.errors.bio)}
              helperText={
                formik.touched.bio && formik.errors.bio
                  ? formik.errors.bio
                  : `${formik.values.bio.length}/1000 characters`
              }
              sx={{
                '& .MuiInputBase-root': {
                  fontSize: { xs: '0.875rem', sm: '1rem' }
                }
              }}
            />
          </Box>
        );

      case 1:
        return (
          <Box sx={{ px: { xs: 1, sm: 0 } }}>
            <Typography variant="h6" gutterBottom sx={{ fontSize: { xs: '1.1rem', sm: '1.25rem' } }}>
              Teaching Experience & Personal Details
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph sx={{ mb: 3 }}>
              Help students understand your background and how to connect with you.
            </Typography>

            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
              <TextField
                fullWidth
                type="number"
                name="experience_years"
                label="Years of Teaching Experience"
                value={formik.values.experience_years}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.experience_years && Boolean(formik.errors.experience_years)}
                helperText={formik.touched.experience_years && formik.errors.experience_years}
                inputProps={{ min: 0, max: 50 }}
                sx={{
                  '& .MuiInputBase-root': {
                    fontSize: { xs: '0.875rem', sm: '1rem' }
                  }
                }}
              />

              <Box sx={{
                display: 'flex',
                flexDirection: { xs: 'column', sm: 'row' },
                gap: 2
              }}>
                <TextField
                  fullWidth
                  name="location"
                  label="Location"
                  placeholder="City, State/Country"
                  value={formik.values.location}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.location && Boolean(formik.errors.location)}
                  helperText={formik.touched.location && formik.errors.location}
                  required
                  sx={{
                    '& .MuiInputBase-root': {
                      fontSize: { xs: '0.875rem', sm: '1rem' }
                    }
                  }}
                />

                <FormControl fullWidth required>
                  <InputLabel>Gender</InputLabel>
                  <Select
                    name="gender"
                    value={formik.values.gender || ''}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    label="Gender"
                    error={formik.touched.gender && Boolean(formik.errors.gender)}
                    sx={{
                      '& .MuiSelect-select': {
                        fontSize: { xs: '0.875rem', sm: '1rem' }
                      }
                    }}
                  >
                    <MenuItem value="male">Male</MenuItem>
                    <MenuItem value="female">Female</MenuItem>
                    <MenuItem value="other">Other</MenuItem>
                    <MenuItem value="prefer_not_to_say">Prefer not to say</MenuItem>
                  </Select>
                  {formik.touched.gender && formik.errors.gender && (
                    <FormHelperText error>{formik.errors.gender}</FormHelperText>
                  )}
                </FormControl>
              </Box>

              <Autocomplete
                multiple
                freeSolo
                options={['English', 'Spanish', 'French', 'German', 'Chinese', 'Japanese', 'Arabic', 'Portuguese', 'Russian', 'Italian']}
                value={formik.values.languages}
                onChange={(_, newValue) => {
                  formik.setFieldValue('languages', newValue.slice(0, 10)); // Limit to 10 languages
                  formik.setFieldTouched('languages', true); // Mark field as touched
                }}
                onBlur={() => {
                  formik.setFieldTouched('languages', true); // Mark field as touched on blur
                }}
                renderTags={(value, getTagProps) =>
                  value.map((option, index) => (
                    <Chip
                      variant="outlined"
                      label={option}
                      size="small"
                      {...getTagProps({ index })}
                      key={index}
                    />
                  ))
                }
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Languages Spoken"
                    placeholder="Type or select languages..."
                    required
                    error={formik.touched.languages && Boolean(formik.errors.languages)}
                    helperText={
                      formik.touched.languages && formik.errors.languages
                        ? formik.errors.languages
                        : `${formik.values.languages.length}/10 languages selected`
                    }
                    sx={{
                      '& .MuiInputBase-root': {
                        fontSize: { xs: '0.875rem', sm: '1rem' }
                      }
                    }}
                  />
                )}
              />

              <TextField
                fullWidth
                name="linkedin_url"
                label="LinkedIn Profile (Optional)"
                placeholder="https://linkedin.com/in/yourprofile"
                value={formik.values.linkedin_url}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.linkedin_url && Boolean(formik.errors.linkedin_url)}
                helperText={formik.touched.linkedin_url && formik.errors.linkedin_url}
                sx={{
                  '& .MuiInputBase-root': {
                    fontSize: { xs: '0.875rem', sm: '1rem' }
                  }
                }}
              />

              <TextField
                fullWidth
                name="website_url"
                label="Personal Website (Optional)"
                placeholder="https://yourwebsite.com"
                value={formik.values.website_url}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.website_url && Boolean(formik.errors.website_url)}
                helperText={formik.touched.website_url && formik.errors.website_url}
                sx={{
                  '& .MuiInputBase-root': {
                    fontSize: { xs: '0.875rem', sm: '1rem' }
                  }
                }}
              />
            </Box>
          </Box>
        );

      case 2:
        return (
          <Box sx={{ px: { xs: 1, sm: 0 } }}>
            <Typography variant="h6" gutterBottom sx={{ fontSize: { xs: '1.1rem', sm: '1.25rem' } }}>
              Your Specializations
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph sx={{ mb: 3 }}>
              Select up to 3 subjects you're qualified to teach. You can type to search or add custom subjects.
            </Typography>

            {isLoadingCourses && apiCourses.length === 0 ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 3 }}>
                <CircularProgress />
                <Typography variant="body2" sx={{ ml: 2 }}>Loading courses...</Typography>
              </Box>
            ) : (
              <Autocomplete
                multiple
                freeSolo
                limitTags={3}
                options={courses}
                getOptionLabel={(option) => {
                  if (typeof option === 'string') return option;
                  return option.name || '';
                }}
                value={[
                  // Include existing courses
                  ...formik.values.specialization_ids.map(id => {
                    const course = courses.find(c => c.id === id);
                    return course;
                  }).filter(Boolean),
                  // Include custom specializations as strings
                  ...formik.values.custom_specializations
                ]}
                onChange={(_, newValue) => {
                  // Limit to 3 selections
                  const limitedValue = newValue.slice(0, 3);

                  const courseIds: number[] = [];
                  const customSpecs: string[] = [];

                  limitedValue.forEach(item => {
                    if (typeof item === 'string') {
                      // Custom specialization
                      customSpecs.push(item);
                    } else if (item && typeof item === 'object' && 'id' in item) {
                      // Existing course
                      courseIds.push(item.id);
                    }
                  });

                  formik.setFieldValue('specialization_ids', courseIds);
                  formik.setFieldValue('custom_specializations', customSpecs);
                }}
                renderTags={(value, getTagProps) =>
                  value.map((option, index) => (
                    <Chip
                      variant="outlined"
                      label={typeof option === 'string' ? option : option.name}
                      size="small"
                      {...getTagProps({ index })}
                      key={index}
                    />
                  ))
                }
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Specializations"
                    placeholder={
                      (formik.values.specialization_ids.length + formik.values.custom_specializations.length) < 3
                        ? "Type or select subjects..."
                        : "Maximum 3 subjects selected"
                    }
                    error={formik.touched.specialization_ids && Boolean(formik.errors.specialization_ids)}
                    helperText={
                      formik.touched.specialization_ids && formik.errors.specialization_ids
                        ? formik.errors.specialization_ids
                        : `${formik.values.specialization_ids.length + formik.values.custom_specializations.length}/3 subjects selected`
                    }
                    fullWidth
                  />
                )}
                sx={{
                  '& .MuiAutocomplete-tag': {
                    margin: '2px',
                  },
                  '& .MuiAutocomplete-inputRoot': {
                    flexWrap: 'wrap',
                  }
                }}
              />
            )}
          </Box>
        );

      case 3:
        return (
          <Box sx={{ px: { xs: 1, sm: 0 } }}>
            <Typography variant="h6" gutterBottom sx={{ fontSize: { xs: '1.1rem', sm: '1.25rem' } }}>
              Rates & Availability
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph sx={{ mb: 3 }}>
              Set your teaching preferences and rates.
            </Typography>

            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
              <Box sx={{
                display: 'flex',
                flexDirection: { xs: 'column', sm: 'row' },
                gap: 2
              }}>
                <TextField
                  fullWidth
                  type="number"
                  name="hourly_rate"
                  label="Hourly Rate"
                  value={formik.values.hourly_rate}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.hourly_rate && Boolean(formik.errors.hourly_rate)}
                  helperText={formik.touched.hourly_rate && formik.errors.hourly_rate}
                  InputProps={{
                    startAdornment: <InputAdornment position="start">₦</InputAdornment>,
                  }}
                  inputProps={{ min: 500, max: 50000 }}
                  sx={{
                    '& .MuiInputBase-root': {
                      fontSize: { xs: '0.875rem', sm: '1rem' }
                    }
                  }}
                />

                <FormControl fullWidth>
                  <InputLabel>Session Type Preference</InputLabel>
                  <Select
                    name="preferred_session_type"
                    value={formik.values.preferred_session_type}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    label="Session Type Preference"
                    sx={{
                      '& .MuiSelect-select': {
                        fontSize: { xs: '0.875rem', sm: '1rem' }
                      }
                    }}
                  >
                    <MenuItem value="online">Online Only</MenuItem>
                    <MenuItem value="in_person">In-Person Only</MenuItem>
                    <MenuItem value="both">Both Online & In-Person</MenuItem>
                  </Select>
                </FormControl>
              </Box>

              <Box sx={{
                display: 'flex',
                flexDirection: { xs: 'column', sm: 'row' },
                gap: 2,
                alignItems: { xs: 'stretch', sm: 'flex-start' }
              }}>
                <TextField
                  fullWidth
                  type="number"
                  name="max_students_per_session"
                  label="Max Students per Session"
                  value={formik.values.max_students_per_session}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.max_students_per_session && Boolean(formik.errors.max_students_per_session)}
                  helperText={formik.touched.max_students_per_session && formik.errors.max_students_per_session}
                  inputProps={{ min: 1, max: 20 }}
                  sx={{
                    '& .MuiInputBase-root': {
                      fontSize: { xs: '0.875rem', sm: '1rem' }
                    }
                  }}
                />

                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  minWidth: { xs: 'auto', sm: '200px' },
                  mt: { xs: 1, sm: 1 }
                }}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formik.values.is_available}
                        onChange={(e) => formik.setFieldValue('is_available', e.target.checked)}
                        name="is_available"
                      />
                    }
                    label="Available for new students"
                    sx={{
                      '& .MuiFormControlLabel-label': {
                        fontSize: { xs: '0.875rem', sm: '1rem' }
                      }
                    }}
                  />
                </Box>
              </Box>
            </Box>
          </Box>
        );

      case 4:
        return (
          <Box sx={{ px: { xs: 1, sm: 0 } }}>
            <Typography variant="h6" gutterBottom sx={{ fontSize: { xs: '1.1rem', sm: '1.25rem' } }}>
              Review Your Profile
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph sx={{ mb: 3 }}>
              Please review your information before creating your tutor profile.
            </Typography>

            <Paper sx={{
              p: { xs: 2, sm: 3 },
              bgcolor: 'background.default',
              borderRadius: 2
            }}>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Box>
                  <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600 }}>
                    Bio:
                  </Typography>
                  <Typography
                    variant="body2"
                    paragraph
                    sx={{
                      fontSize: { xs: '0.875rem', sm: '1rem' },
                      lineHeight: 1.5,
                      mb: 2
                    }}
                  >
                    {formik.values.bio}
                  </Typography>
                </Box>

                <Box sx={{
                  display: 'grid',
                  gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr' },
                  gap: 2
                }}>
                  <Box>
                    <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600 }}>
                      Experience:
                    </Typography>
                    <Typography variant="body2" sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                      {formik.values.experience_years} years
                    </Typography>
                  </Box>

                  <Box>
                    <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600 }}>
                      Hourly Rate:
                    </Typography>
                    <Typography variant="body2" sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                      ${formik.values.hourly_rate}/hour
                    </Typography>
                  </Box>

                  {formik.values.location && (
                    <Box>
                      <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600 }}>
                        Location:
                      </Typography>
                      <Typography variant="body2" sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                        {formik.values.location}
                      </Typography>
                    </Box>
                  )}

                  {formik.values.gender && (
                    <Box>
                      <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600 }}>
                        Gender:
                      </Typography>
                      <Typography variant="body2" sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                        {formik.values.gender === 'prefer_not_to_say' ? 'Prefer not to say' :
                         formik.values.gender.charAt(0).toUpperCase() + formik.values.gender.slice(1)}
                      </Typography>
                    </Box>
                  )}

                  <Box>
                    <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600 }}>
                      Session Type:
                    </Typography>
                    <Typography variant="body2" sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                      {formik.values.preferred_session_type === 'online' ? 'Online Only' :
                       formik.values.preferred_session_type === 'in_person' ? 'In-Person Only' :
                       'Both Online & In-Person'}
                    </Typography>
                  </Box>

                  <Box>
                    <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600 }}>
                      Max Students:
                    </Typography>
                    <Typography variant="body2" sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
                      {formik.values.max_students_per_session} per session
                    </Typography>
                  </Box>
                </Box>

                {formik.values.languages.length > 0 && (
                  <Box>
                    <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600 }}>
                      Languages:
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {formik.values.languages.map((language, index) => (
                        <Chip
                          key={index}
                          label={language}
                          size="small"
                          variant="outlined"
                          sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}
                        />
                      ))}
                    </Box>
                  </Box>
                )}

                <Box>
                  <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600 }}>
                    Specializations:
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {formik.values.specialization_ids.map((id) => {
                      const course = courses.find(c => c.id === id);
                      return (
                        <Chip
                          key={id}
                          label={course?.name || `Subject ${id}`}
                          size="small"
                          variant="outlined"
                          sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}
                        />
                      );
                    })}
                  </Box>
                </Box>
              </Box>
            </Paper>
          </Box>
        );

      default:
        return 'Unknown step';
    }
  };

  if (isLoadingCourses && apiCourses.length === 0 && courses.length === 0) {
    return (
      <Container maxWidth="md" sx={{ py: { xs: 2, sm: 4 }, px: { xs: 1, sm: 3 } }}>
        <Box sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: '50vh',
          gap: 2
        }}>
          <CircularProgress />
          <Typography variant="body1" color="text.secondary">
            Loading courses...
          </Typography>
        </Box>
      </Container>
    );
  }

  return (
    <Container
      maxWidth="md"
      sx={{
        py: { xs: 1, sm: 4 },
        px: { xs: 0.5, sm: 3 },
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      <Paper
        elevation={3}
        sx={{
          p: { xs: 1.5, sm: 4 },
          borderRadius: { xs: 1, sm: 3 },
          flex: 1,
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        {/* Header */}
        <Box sx={{ textAlign: 'center', mb: { xs: 2, sm: 4 } }}>
          <School sx={{ fontSize: { xs: 32, sm: 48 }, color: 'primary.main', mb: 1 }} />
          <Typography variant="h5" gutterBottom sx={{ fontSize: { xs: '1.25rem', sm: '2rem' } }}>
            Complete Your Tutor Profile
          </Typography>
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{
              px: { xs: 0.5, sm: 0 },
              fontSize: { xs: '0.875rem', sm: '1rem' }
            }}
          >
            Set up your tutoring profile to start connecting with students
          </Typography>
        </Box>

        {/* Mobile Stepper - Simplified */}
        <Box sx={{
          mb: { xs: 2, sm: 4 },
          display: { xs: 'block', sm: 'none' }
        }}>
          <Typography variant="body2" color="text.secondary" align="center">
            Step {activeStep + 1} of {steps.length}: {steps[activeStep]}
          </Typography>
          <Box sx={{
            display: 'flex',
            justifyContent: 'center',
            mt: 1,
            gap: 0.5
          }}>
            {steps.map((_, index) => (
              <Box
                key={index}
                sx={{
                  width: 8,
                  height: 8,
                  borderRadius: '50%',
                  bgcolor: index <= activeStep ? 'primary.main' : 'grey.300',
                  transition: 'background-color 0.3s'
                }}
              />
            ))}
          </Box>
        </Box>

        {/* Desktop Stepper */}
        <Stepper
          activeStep={activeStep}
          sx={{
            mb: { xs: 0, sm: 4 },
            display: { xs: 'none', sm: 'flex' }
          }}
        >
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {/* Error message */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Course loading error */}
        {coursesError && (
          <Alert severity="error" sx={{ mb: 3 }}>
            Failed to load courses. Please refresh the page.
          </Alert>
        )}

        {/* Profile completion success message */}
        {profileComplete ? (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <CheckCircle sx={{ fontSize: 64, color: 'success.main', mb: 2 }} />
            <Typography variant="h5" gutterBottom>
              Tutor Profile Created!
            </Typography>
            <Typography variant="body1" color="text.secondary" paragraph>
              Your tutor profile has been successfully created. Redirecting to your dashboard...
            </Typography>
            <CircularProgress size={24} sx={{ mt: 2 }} />
          </Box>
        ) : (
          <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
            <form onSubmit={formik.handleSubmit} style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
              {/* Step content */}
              <Box sx={{ flex: 1, overflow: 'auto', mb: 2 }}>
                {getStepContent(activeStep)}
              </Box>

              {/* Navigation buttons */}
              <Box sx={{
                display: 'flex',
                justifyContent: 'space-between',
                mt: 'auto',
                pt: 2,
                gap: 2,
                flexDirection: { xs: 'column', sm: 'row' },
                borderTop: '1px solid',
                borderColor: 'divider'
              }}>
                <Button
                  disabled={activeStep === 0}
                  onClick={handleBack}
                  variant="outlined"
                  size="large"
                  sx={{
                    order: { xs: 2, sm: 1 },
                    minHeight: { xs: '48px', sm: '40px' }
                  }}
                >
                  Back
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  disabled={createProfileMutation.isLoading}
                  startIcon={createProfileMutation.isLoading ? <CircularProgress size={20} /> : null}
                  size="large"
                  sx={{
                    order: { xs: 1, sm: 2 },
                    minHeight: { xs: '48px', sm: '40px' }
                  }}
                >
                  {activeStep === steps.length - 1 ? 'Create Profile' : 'Next'}
                </Button>
              </Box>
            </form>
          </Box>
        )}
      </Paper>
    </Container>
  );
};

export default TutorProfileCompletion;
